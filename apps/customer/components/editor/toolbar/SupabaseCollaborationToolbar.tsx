'use client'

import React, { useState } from 'react'
import { Editor } from '@tiptap/react'
import { Button } from '@ui/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import {
  MessageSquare,
  History,
  Share2,
  Settings,
  Download,
  Users,
  Wifi,
  WifiOff,
  FileImage,
  FileSpreadsheet,
  FileText
} from 'lucide-react'
import { usePresence } from '../hooks/usePresence'

export type ExportFormat = 'pdf' | 'docx' | 'html' | 'md'

interface SupabaseCollaborationToolbarProps {
  editor: Editor
  documentId: string
  currentUser: {
    id: string
    name: string
    email?: string
    avatar?: string
    color?: string
  }
  showComments: boolean
  showHistory: boolean
  onToggleComments: () => void
  onToggleHistory: () => void
  onShare: () => void
  onSettings: () => void
  onExport: (format: ExportFormat) => void
  // Feature flags for controlling which buttons are available
  featureFlags?: {
    comments?: boolean
    share?: boolean
    exportWord?: boolean
    exportMarkdown?: boolean
    exportHtml?: boolean
  }
}

export function SupabaseCollaborationToolbar({
  editor,
  documentId,
  currentUser,
  showComments,
  showHistory,
  onToggleComments,
  onToggleHistory,
  onShare,
  onSettings,
  onExport,
  featureFlags = {},
}: SupabaseCollaborationToolbarProps) {
  // Use the presence hook for real-time user presence
  const { activeUsers, isConnected } = usePresence({
    documentId,
    user: currentUser,
    editor
  })

  return (
    <div className="flex items-center justify-between px-4 py-2 border-b bg-white shadow-sm" data-testid="collaboration-toolbar">
      {/* Left side - Connection status and active users */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="w-4 h-4 text-green-500" />
          ) : (
            <WifiOff className="w-4 h-4 text-red-500" />
          )}
          <span className="text-sm text-gray-600">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>

        {/* Active users */}
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-gray-500" />
          <div className="flex -space-x-2">
            {activeUsers.slice(0, 5).map((user) => (
              <div
                key={user.id}
                className="w-8 h-8 rounded-full border-2 border-white flex items-center justify-center text-xs font-medium text-white"
                style={{ backgroundColor: user.color }}
                title={`${user.name} (online)`}
              >
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  user.name.charAt(0).toUpperCase()
                )}
              </div>
            ))}
            {activeUsers.length > 5 && (
              <div className="w-8 h-8 rounded-full border-2 border-white bg-gray-500 flex items-center justify-center text-xs font-medium text-white">
                +{activeUsers.length - 5}
              </div>
            )}
          </div>
          <span className="text-sm text-gray-500">
            {activeUsers.length} online
          </span>
        </div>
      </div>

      {/* Right side - Action buttons */}
      <div className="flex items-center space-x-2">
        {featureFlags.comments !== false && (
          <Button
            variant={showComments ? "default" : "outline"}
            size="sm"
            onClick={onToggleComments}
            title="Comments"
            className="flex items-center space-x-1"
            data-testid="comments-button"
          >
            <MessageSquare className="w-4 h-4" />
            <span>Comments</span>
          </Button>
        )}

        <Button
          variant={showHistory ? "default" : "outline"}
          size="sm"
          onClick={onToggleHistory}
          title="History"
          className="flex items-center space-x-1"
          data-testid="history-button"
        >
          <History className="w-4 h-4" />
          <span>History</span>
        </Button>

        {featureFlags.share !== false && (
          <Button
            variant="outline"
            size="sm"
            onClick={onShare}
            title="Share"
            className="flex items-center space-x-1"
            data-testid="share-button"
          >
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </Button>
        )}

        {/* Export */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center space-x-1"
              data-testid="export-button"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onExport('pdf')}>
              <FileImage className="w-4 h-4 mr-2" />
              Export as PDF
            </DropdownMenuItem>
            {featureFlags.exportWord !== false && (
              <DropdownMenuItem onClick={() => onExport('docx')}>
                <FileSpreadsheet className="w-4 h-4 mr-2" />
                Export as Word
              </DropdownMenuItem>
            )}
            {featureFlags.exportMarkdown !== false && (
              <DropdownMenuItem onClick={() => onExport('md')}>
                <FileText className="w-4 h-4 mr-2" />
                Export as Markdown
              </DropdownMenuItem>
            )}
            {featureFlags.exportHtml !== false && (
              <DropdownMenuItem onClick={() => onExport('html')}>
                <FileText className="w-4 h-4 mr-2" />
                Export as HTML
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          variant="outline"
          size="sm"
          onClick={onSettings}
          title="Settings"
          className="flex items-center space-x-1"
          data-testid="settings-button"
        >
          <Settings className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}
