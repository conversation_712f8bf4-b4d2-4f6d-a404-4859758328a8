import { expect, test } from '@playwright/test'
import { TestUtils } from '../helpers/tests/test-utils'

test.describe('Issue EKO-142: Fixed Editor AI Features Test', () => {
  let testUtils: TestUtils

  test.beforeEach(async ({ page }) => {
    testUtils = new TestUtils(page)
    await testUtils.login()
  })

  test('should display AI toolbar with all command buttons - EKO-142', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()

    // AI toolbar should be visible
    await expect(page.locator('[data-testid="ai-toolbar"]')).toBeVisible()

    // Check all AI command buttons are present using data-testid
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-fix-grammar"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-make-shorter"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-make-longer"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-change-tone"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-summarize"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-continue"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-brainstorm"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-chat-button"]')).toBeVisible()
  })

  test('should handle AI commands with text selection - EKO-142', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()
    
    // Add some text to work with
    await testUtils.typeInEditor('This is some text that needs improvement and has grammar errors.')
    
    // Use JavaScript to programmatically select all text in the editor
    await page.evaluate(() => {
      const editor = document.querySelector('.ProseMirror') as HTMLElement;
      const range = document.createRange();
      range.selectNodeContents(editor);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
    })
    
    // Wait for selection to register
    await page.waitForTimeout(1000)
    
    // Mock AI API to avoid actual AI calls in tests
    await testUtils.mockApiResponse('/api/ai/generate', {
      result: 'This text has been improved and corrected.',
      success: true
    })
    
    // Wait for button to be enabled (indicating text is selected)
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeEnabled({ timeout: 5000 })
    
    // Test Improve Writing command
    await page.click('[data-testid="ai-command-improve"]')
    
    // Wait for processing to complete
    await page.waitForTimeout(2000)
  })

  test('should open and close AI chat panel - EKO-142', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()
    
    // Click AI chat button
    await page.click('[data-testid="ai-chat-button"]')
    
    // Check AI chat panel opens
    await expect(page.locator('[data-testid="ai-chat-panel"]')).toBeVisible()
    
    // Check chat interface elements
    await expect(page.locator('[data-testid="ai-chat-input"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-chat-send-button"]')).toBeVisible()
    
    // Close panel by clicking the close button
    await page.click('[data-testid="close-side-panel"]')
    await expect(page.locator('[data-testid="ai-chat-panel"]')).not.toBeVisible()
  })

  test('should handle AI slash commands - EKO-142', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()

    const editor = await testUtils.waitForEditor()

    // Click in editor and type slash command
    await editor.click()
    await page.keyboard.type('/ai')

    // Should show "AI Commands" header
    await expect(page.locator('[data-testid="ai-commands-header"]')).toBeVisible()

    // Should show command options
    await expect(page.locator('[data-testid="ai-slash-command-improve"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-grammar"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-shorter"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-expand"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-tone"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-summarize"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-continue"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-slash-command-custom"]')).toBeVisible()

    // Click on improve writing command
    await page.click('[data-testid="ai-slash-command-improve"]')

    // The slash command text should be removed
    await expect(page.locator('[data-testid="eko-document-editor"] .prose')).not.toContainText('/ai')
  })

  test('should verify required AI features are present - EKO-142', async ({ page }) => {
    await testUtils.createDocumentFromTemplate()
    await testUtils.waitForEditor()
    
    // All AI features should be available using data-testid selectors
    await expect(page.locator('[data-testid="ai-toolbar"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-improve"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-fix-grammar"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-make-shorter"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-make-longer"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-change-tone"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-summarize"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-continue"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-command-brainstorm"]')).toBeVisible()
    await expect(page.locator('[data-testid="ai-chat-button"]')).toBeVisible()
    
    // Test that core functionality works
    await testUtils.typeInEditor('All AI features are available and working')
    await testUtils.checkEditorContent('All AI features are available and working')
    
    // Verify document can be saved and loaded
    const documentId = testUtils.getDocumentIdFromUrl()
    expect(documentId).toBeTruthy()
  })
})
